import { useState, useEffect } from 'react';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';

import classes from './assessment.module.scss';
import Dropdown from '../controls/Dropdown';
import DatePicker from '../controls/DatePicker';
import MultiSelect from '../controls/MultiSelect';
import MultiSelectModel from '../../models/MultiSelectModel';
import { commonServices } from '../../services/commonService';
// import { Date | null } from "@material-ui/pickers/typings/date";
import ModalFooter from '../controls/ModalFooter';
import {
  AssessmentStatus,
  DialogAction,
  UserRoleEnum,
  UserStatus,
} from '../../models/Enums';
import {
  CreateAssessmentModel,
  UpdateAssessmentRequestModel,
} from '../../models/AssessmentModel';
import { assessmentService } from '../../services/assessmentService';
import { userService } from '../../services/userService';
import { CurrentUserModel } from '../../models/ProfileModel';
import { authService } from '../../services/authService';
import { useDispatch } from 'react-redux';
import { updateStrategySelection } from '../../redux/ducks/scopedefinition';
import { StrategySelectionModel } from '../../models/ScopeDefinitionModel';
import { UtilityHelper } from '../../utils/UtilityHelper';

type AddAssessmentProps = {
  assessmentId?: string;
  onDialogClose: (
    action: DialogAction,
    assessmentId?: string,
    country?: string
  ) => void;
  assessmentStatus?: number;
};

/** Renders the Add Assessment screen */
const AddAssessment = (props: AddAssessmentProps) => {
  const { assessmentId, onDialogClose, assessmentStatus } = props;
  const { t } = useTranslation();
  const dispatch = useDispatch();
  const [countries, setCountries] = useState<Array<MultiSelectModel>>([]);
  const [assessment, setAssessment] = useState<CreateAssessmentModel>(
    CreateAssessmentModel.init()
  );
  const [users, setUsers] = useState<Array<MultiSelectModel>>([]);
  const [managers, setManagers] = useState<Array<MultiSelectModel>>([]);
  const [error, setError] = useState<any>({});
  const userInfo: CurrentUserModel = authService.getCurrentUser();

  const [canChangeManager, setCanChangeManager] = useState<boolean | null>(
    false
  );

  useEffect(() => {
    commonServices
      .getCountriesForCurrentUser()
      .then((records: Array<MultiSelectModel>) => {
        setCountries(records);
        if (assessmentId) {
          // call an api to bind the assessment in edit mode
          assessmentService
            .getAssessmentById(assessmentId)
            .then((response: CreateAssessmentModel) => {
              setCanChangeManager(response?.permissions?.canChangeManager);

              const startDateStr = response.startDate.toString().split('T')[0];
              const startDateParts = startDateStr.split('-');
              const startDate = new Date(
                parseInt(startDateParts[0]),
                parseInt(startDateParts[1]) - 1,
                parseInt(startDateParts[2])
              );
              response.startDate = startDate;

              const endDateStr = response.endDate.toString().split('T')[0];
              const endDateParts = endDateStr.split('-');
              const endDate = new Date(
                parseInt(endDateParts[0]),
                parseInt(endDateParts[1]) - 1,
                parseInt(endDateParts[2])
              );
              response.endDate = endDate;

              setAssessment(response);
              bindUsers(response.countryId);
              bindManagers(response.countryId);
            });
        }
      });
  }, []);

  // Triggered by all HTMLInputElement whenever the values are changed
  const onChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    if (evt.target.name === 'countryId') {
      bindUsers(evt.target.value);
      bindManagers(evt.target.value);
    }
    if (evt.target.name === 'manager') {
      // filter out manager from user list, it shouldn't show up in editors and reviewers dropdown
      onManagerChange(evt.target.name, evt.target.value);
    } else {
      setAssessment({
        ...assessment,
        [evt.target.name]: evt.target.value,
      });
    }

    removeControlValidationOnChange(evt.target.value, evt.target.name);
  };

  // triggers whenever user changes the manager's dropdown
  const onManagerChange = (field: string, value: string) => {
    const updatedUsers = [...managers];
    // if already selected in reviewers and editors remove it
    const updatedAssessment = { ...assessment };
    updatedAssessment.editors = updatedAssessment.editors.filter(
      (editor: string) => editor !== value
    );
    updatedAssessment.reviewers = updatedAssessment.reviewers.filter(
      (reviewer: string) => reviewer !== value
    );

    setAssessment({
      ...updatedAssessment,
      [field]: value,
    });
  };

  //bind managers to state when country selection change
  const bindManagers = (countryId: string) => {
    if (countryId) {
      userService.getManagersByCountry(countryId).then((result: any) => {
        const managers = result.map((data: any) => {
          return new MultiSelectModel(data.id, data.name);
        });
        setManagers(managers);
      });
    }
  };

  // bind users grid
  const bindUsers = (countryId: string) => {
    if (countryId) {
      userService.getUsersByCountry(countryId).then((result: any) => {
        const users = result
          .filter((user: any) => user.userType == UserRoleEnum.Viewer)
          .map((data: any) => {
            return new MultiSelectModel(data.id, data.name);
          });

        //set managers and super manager value when you create assessment
        if (!assessmentId) {
          if (userInfo.userType === UserRoleEnum.SuperManager) {
            setAssessment({
              ...assessment,
              countryId: countryId,
            });
            setCanChangeManager(true);
          } else {
            setAssessment({
              ...assessment,
              manager: userInfo.userId,
              countryId: countryId,
            });
            setCanChangeManager(false);
          }
        }
        setUsers(users);
      });
    }
  };

  // triggered by multiselect change event
  const onMultipSelectChange = (
    selectedValues: Array<MultiSelectModel>,
    field: string
  ) => {
    setAssessment({
      ...assessment,
      [field]: selectedValues.map(({ id }) => id),
    });
    removeControlValidationOnChange(selectedValues, field);
  };

  // triggers whenever user tries to modify the change
  const onDateChange = (date: Date | null, field: string) => {
    // Convert string date to Date object for proper serialization
    //"2026/06/09"
    const dateValue = date
      ? new Date(date.getFullYear(), date.getMonth(), date.getDate())
      : null;
    setAssessment({
      ...assessment,
      [field]: dateValue,
    });
    removeControlValidationOnChange(date?.toString() as string, field);
  };

  // create an assessment
  const createUpdateAssessment = () => {
    if (!isFormValid()) {
      setError(validate());
      return;
    }

    if (validateSameUserAssignedAsDifferentRoles()) {
      return;
    }

    const country =
      countries.find(
        (country: MultiSelectModel) => country.id === assessment.countryId
      )?.text || '';

    if (!assessmentId) {
      assessmentService.createAssessment(assessment).then(assessmentId => {
        if (assessmentId) {
          onDialogClose(DialogAction.Add, assessmentId as string, country);
        }

        //Tracking add assessment event in analytics
        UtilityHelper.onEventAnalytics(
          'Assessment',
          'Add new assessment',
          'Add assessment'
        );
      });
    } else {
      const { countryId, startDate, endDate, manager, reviewers, editors } =
        assessment;
      const updateAssessment: UpdateAssessmentRequestModel =
        new UpdateAssessmentRequestModel(
          assessmentId,
          countryId,
          startDate,
          endDate,
          manager,
          editors,
          reviewers
        );

      assessmentService
        .updateAssessment(updateAssessment)
        .then(assessmentId => {
          if (assessmentId) {
            onDialogClose(DialogAction.Edit, assessmentId as string);
          }

          //Tracking update assessment event in analytics
          UtilityHelper.onEventAnalytics(
            'Assessment',
            'Update assessment',
            'Update assessment'
          );
        });
    }

    {
      /*cleanup redux strategy selection once it goes to the above URL*/
    }
    dispatch(updateStrategySelection(StrategySelectionModel.init()));
  };

  // checks and remove validation for the control if satisfies the validate condition
  const removeControlValidationOnChange = (
    value: string | Array<MultiSelectModel>,
    field: string
  ) => {
    if (value?.length > 0) {
      const formError = { ...error };
      delete formError[field];
      setError({ ...formError });
    } else {
      setError({ ...error, [field]: 'Field is mandatory' });
    }
  };
  const isFormValid = () => Object.keys(validate()).length === 0;

  // validate if same user already been assigned to different role
  const validateSameUserAssignedAsDifferentRoles = () => {
    const alreadyAssigned =
      assessment.editors.filter(
        (editor: string) =>
          // check if user already assigned in any of the Dropdown selection i.e. Manager, Editors, Reviewers
          assessment.reviewers.includes(editor) ||
          assessment.reviewers.includes(assessment.manager) ||
          assessment.editors.includes(assessment.manager)
      ).length > 0;

    if (alreadyAssigned) {
      setError({
        ...error,
        userAlreadyAssigned: t('Assessment.SameUserDifferentRole'),
      });
    } else {
      const updatedError = { ...error };
      delete updatedError['userAlreadyAssigned'];
      setError(updatedError);
    }

    return alreadyAssigned;
  };

  // validate form
  const validate = () => {
    let error: any = {};
    const ignoreRequiredFields = ['editors', 'reviewers', 'manager'];
    Object.keys(assessment)
      .filter((key: string) => !ignoreRequiredFields.includes(key))
      .map((key: string) => {
        const assessmentModel = assessment as any;
        const value = assessmentModel[key];

        // Check if field is empty based on its type
        const isEmpty =
          key === 'startDate' || key === 'endDate'
            ? !value || (value instanceof Date && isNaN(value.getTime()))
            : !value ||
              (typeof value === 'string' && value.length === 0) ||
              (Array.isArray(value) && value.length === 0);

        if (isEmpty) {
          error = { ...error, [key]: t('Errors.MandatoryField') };
        } else {
          if (key == 'startDate' || key == 'endDate') {
            const dateValue = value;
            const checkDate: boolean =
              dateValue instanceof Date
                ? UtilityHelper.isValidDate(dateValue)
                : UtilityHelper.isValidDate(new Date(dateValue));
            if (!checkDate) {
              error = { ...error, [key]: t('Errors.InvalidDate') };
            } else {
              delete error[key];
            }
          } else {
            delete error[key];
          }
        }
        return error;
      });

    // Additional validation for date range
    if (assessment.startDate && assessment.endDate) {
      const startDate =
        assessment.startDate instanceof Date
          ? assessment.startDate
          : new Date(assessment.startDate);
      const endDate =
        assessment.endDate instanceof Date
          ? assessment.endDate
          : new Date(assessment.endDate);

      // Normalize dates to compare only the date part (ignore time)
      const normalizedStartDate = new Date(
        startDate.getFullYear(),
        startDate.getMonth(),
        startDate.getDate()
      );
      const normalizedEndDate = new Date(
        endDate.getFullYear(),
        endDate.getMonth(),
        endDate.getDate()
      );

      if (normalizedStartDate >= normalizedEndDate) {
        error = {
          ...error,
          endDate:
            t('Errors.InvalidDateRange') || 'End date must be after start date',
        };
      }
    }

    return error;
  };

  return (
    <>
      <div className={classNames(classes.formWrapper)}>
        <div className='row mb-3'>
          <div className='col-xs-12 col-md-6'>
            <Dropdown
              id='countryId'
              name='countryId'
              disabled={!!assessmentId}
              value={assessment.countryId}
              options={countries}
              label={t('UserManagement.GridColumn.Country')}
              error={error['countryId'] ? true : false}
              helperText={error['countryId']}
              onChange={onChange}
              InputLabelProps={{ required: true, shrink: true }}
            />
          </div>
        </div>

        <div className='row mb-3'>
          <div className='col-xs-12 col-md-6'>
            <DatePicker
              value={assessment.startDate}
              label={t('Common.StartDate')}
              disablePast
              error={error['startDate'] ? true : false}
              helperText={error['startDate']}
              onAccept={(date: Date | null) => onDateChange(date, 'startDate')}
              InputLabelProps={{ required: true, shrink: true }}
              openTo='year'
              views={['year', 'month', 'date']}
              disabled={assessmentStatus == AssessmentStatus.Published}
              placeholder={t('Common.YearMonthDate')}
              onKeyDown={e => {
                e.preventDefault();
              }}
            />
          </div>
          <div className='col-xs-12 col-md-6'>
            <DatePicker
              value={assessment.endDate}
              label={t('Assessment.ExpectedEndDate')}
              disablePast
              minDate={assessment.startDate}
              error={error['endDate'] ? true : false}
              helperText={error['endDate']}
              onAccept={(date: Date | null) => onDateChange(date, 'endDate')}
              InputLabelProps={{ required: true, shrink: true }}
              openTo='year'
              views={['year', 'month', 'date']}
              disabled={assessmentStatus == AssessmentStatus.Published}
              placeholder={t('Common.YearMonthDate')}
              onKeyDown={e => {
                e.preventDefault();
              }}
            />
          </div>
        </div>

        <h6 className='mb-4 modal-content-head'>
          {t('Assessment.AssignUserRole')}
        </h6>

        <div className='row mb-3'>
          <div className='col-xs-12 col-md-6'>
            <Dropdown
              id='manager'
              name='manager'
              options={managers}
              value={assessment.manager}
              label={t('Assessment.Manager')}
              onChange={onChange}
              disabled={assessmentStatus == AssessmentStatus.Published}
            />
          </div>
        </div>

        <div className='row mb-3'>
          <div className='col-xs-12 col-md-6'>
            <MultiSelect
              id='editors'
              values={users.filter((user: MultiSelectModel) => {
                return assessment.editors.includes(user.id);
              })}
              label={t('Assessment.Editors')}
              options={users}
              onUpdate={(selectedOptions: MultiSelectModel[]) => {
                onMultipSelectChange(selectedOptions, 'editors');
              }}
            />
            <small className='fw-lighter'>
              {t('Assessment.EditorsTooltip')}
            </small>
          </div>
          <div className='col-xs-12 col-md-6'>
            <MultiSelect
              id='reviewers'
              values={users.filter((user: MultiSelectModel) => {
                return assessment.reviewers.includes(user.id);
              })}
              label={t('Assessment.Reviewers')}
              options={users}
              onUpdate={(selectedOptions: MultiSelectModel[]) =>
                onMultipSelectChange(selectedOptions, 'reviewers')
              }
            />
            <small className='fw-lighter'>
              {t('Assessment.ReviewersTooltip')}
            </small>
          </div>
        </div>

        <div className='mb-3'>
          {error['userAlreadyAssigned'] && (
            <p className='text-danger'>{error['userAlreadyAssigned']}</p>
          )}
          <ModalFooter>
            <>
              <button
                className='btn app-btn-secondary'
                onClick={() => onDialogClose(DialogAction.Close)}
              >
                {t('Common.Cancel')}
              </button>
              <button
                className='btn app-btn-primary'
                onClick={createUpdateAssessment}
              >
                {!assessmentId
                  ? t('Assessment.AddAssessment')
                  : t('Common.Update')}
              </button>
            </>
          </ModalFooter>
        </div>
      </div>
    </>
  );
};

export default AddAssessment;
